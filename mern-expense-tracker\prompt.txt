# 📘 Project Documentation: Expense Tracking System – MERN Stack

## 🔰 Overview

This project is a full-stack web application that helps users track their daily and monthly expenses. Users can register/login, add expenses with categories, view and filter expenses, and see visual charts of their spending habits.

---

## 🧱 Tech Stack

* **Frontend**: React.js, Axios, React Router, Chart.js or Recharts
* **Backend**: Node.js, Express.js
* **Database**: MongoDB Atlas
* **Authentication**: JWT (JSON Web Tokens)
* **UI Styling**: TailwindCSS or Bootstrap (optional)

---

## 🔧 Step-by-Step Development

---

### ✅ Step 1: Project Setup

1. Create a root folder for the project.
2. Inside it, create two subfolders: one for the frontend (`client`) and one for the backend (`server`).
3. Initialize React project using `create-react-app` inside the client folder.
4. Initialize the backend with `npm init` and install necessary backend packages.
5. Set up Git for version control and create a `.gitignore` file for both client and server.

---

### ✅ Step 2: Setup MongoDB Atlas

1. Go to [https://www.mongodb.com/cloud/atlas](https://www.mongodb.com/cloud/atlas) and create a free cluster.
2. Create a database named `expense_tracker`.
3. Create a collection named `users` and another named `expenses`.
4. Whitelist your IP and create a database user with a password.
5. Copy the connection URI string and store it in a `.env` file in your backend project.

---

### ✅ Step 3: Backend Development

#### 3.1 Express Server Setup

1. Set up a basic Express server and connect it to MongoDB Atlas using Mongoose.
2. Create a `.env` file for MongoDB URI, port, and JWT secret.
3. Enable middleware: CORS, JSON parsing.

#### 3.2 User Authentication

1. Create a `User` model with fields like name, email, and hashed password.
2. Implement authentication routes: register and login.
3. Use `bcrypt` to hash passwords before storing.
4. Use JWT to generate tokens upon login.
5. Create an authentication middleware to protect private routes.

#### 3.3 Expense Management

1. Create an `Expense` model with fields: userId, title, amount, category, and date.
2. Implement routes for CRUD operations: add, get, update, delete expenses.
3. Protect all routes using JWT middleware so only logged-in users can access their data.

---

### ✅ Step 4: Frontend Development

#### 4.1 Basic Structure

1. Set up React Router for navigation between pages.
2. Create necessary pages: Register, Login, Dashboard, Add Expense.
3. Create a context or state management for user authentication.

#### 4.2 Auth Integration

1. Build forms for registration and login.
2. On login, store JWT token in `localStorage`.
3. Use Axios to send authenticated requests to backend routes.

#### 4.3 Expense Management UI

1. Build a dashboard that displays total expenses, recent transactions, and a form to add new expenses.
2. Display expenses in a table or list format with options to edit/delete.
3. Implement filtering by category, date range, and amount.

---

### ✅ Step 5: Charts and Visualization

1. Use a chart library like Recharts or Chart.js.
2. Display a pie chart for category-wise expenses.
3. Show a bar chart for monthly spending trends.
4. Update chart dynamically when new data is added or deleted.

---

### ✅ Step 6: Extra Features (Optional)

1. Add a monthly budget tracker and alert user if they exceed it.
2. Add export to CSV feature for downloading expense history.
3. Add recurring expenses feature (e.g., rent every month).
4. Add a dark/light theme toggle.
5. Optimize for mobile devices.

---

### ✅ Step 7: Deployment

#### 7.1 Deploy Backend

1. Host backend using Render, Railway, or Vercel.
2. Make sure to update `.env` variables in the hosted environment.

#### 7.2 Deploy Frontend

1. Build your React app using `npm run build`.
2. Deploy using Vercel or Netlify.
3. Update frontend URLs to match the hosted backend.

---

### ✅ Step 8: Final Touch

1. Test all major features and edge cases.
2. Add user-friendly error messages and loading indicators.
3. Write a README file with project description, tech stack, features, and setup instructions.
4. Optional: Record a demo video or add screenshots.

---

## 📂 Suggested Folder Structure

```
mern-expense-tracker/
├── client/
│   ├── public/
│   ├── src/
│   │   ├── pages/
│   │   ├── components/
│   │   ├── services/
│   │   ├── App.js
│   │   └── index.js
├── server/
│   ├── controllers/
│   ├── routes/
│   ├── models/
│   ├── middleware/
│   ├── server.js
│   └── .env
```

---

## 📌 Summary

This MERN Expense Tracker project will cover all the core aspects of full-stack development:

* CRUD operations
* REST APIs
* JWT Authentication
* State management
* Data visualization
* MongoDB Atlas integration
* Deployment best practices

---
