import React from 'react';
import { useAuth } from '../context/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Welcome back, {user?.name}!</h1>
        <p>Here's an overview of your expenses</p>
      </div>
      
      <div className="stats-grid">
        <div className="stat-card">
          <h3>$0</h3>
          <p>Total Expenses</p>
        </div>
        <div className="stat-card">
          <h3>$0</h3>
          <p>This Month</p>
        </div>
        <div className="stat-card">
          <h3>0</h3>
          <p>Total Transactions</p>
        </div>
        <div className="stat-card">
          <h3>$0</h3>
          <p>Average per Day</p>
        </div>
      </div>
      
      <div className="card">
        <h3>Recent Expenses</h3>
        <p style={{ textAlign: 'center', color: '#666', padding: '40px' }}>
          No expenses found. Start by adding your first expense!
        </p>
      </div>
    </div>
  );
};

export default Dashboard;
