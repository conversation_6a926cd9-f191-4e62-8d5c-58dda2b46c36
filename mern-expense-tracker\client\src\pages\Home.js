import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Home = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div>
      <section className="hero">
        <div className="container">
          <h1>Track Your Expenses Effortlessly</h1>
          <p>
            Take control of your finances with our intuitive expense tracking system.
            Monitor your spending, categorize expenses, and visualize your financial habits.
          </p>
          {!isAuthenticated ? (
            <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
              <Link to="/register" className="btn btn-success">
                Get Started
              </Link>
              <Link to="/login" className="btn btn-primary">
                Login
              </Link>
            </div>
          ) : (
            <Link to="/dashboard" className="btn btn-primary">
              Go to Dashboard
            </Link>
          )}
        </div>
      </section>

      <section className="features">
        <div className="container">
          <div className="feature-card">
            <h3>📊 Visual Analytics</h3>
            <p>
              Get insights into your spending patterns with beautiful charts and graphs.
              See where your money goes with category-wise breakdowns.
            </p>
          </div>
          
          <div className="feature-card">
            <h3>🏷️ Smart Categorization</h3>
            <p>
              Organize your expenses into categories like food, transportation, entertainment,
              and more for better financial planning.
            </p>
          </div>
          
          <div className="feature-card">
            <h3>📱 Easy to Use</h3>
            <p>
              Simple and intuitive interface that makes expense tracking a breeze.
              Add, edit, and delete expenses with just a few clicks.
            </p>
          </div>
          
          <div className="feature-card">
            <h3>🔒 Secure & Private</h3>
            <p>
              Your financial data is protected with industry-standard security measures.
              Only you have access to your expense information.
            </p>
          </div>
          
          <div className="feature-card">
            <h3>📈 Monthly Trends</h3>
            <p>
              Track your spending trends over time and identify patterns in your
              financial behavior to make better decisions.
            </p>
          </div>
          
          <div className="feature-card">
            <h3>💡 Smart Insights</h3>
            <p>
              Get personalized insights and recommendations to help you save money
              and achieve your financial goals.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
